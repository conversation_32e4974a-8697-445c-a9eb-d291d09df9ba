﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyVersion>1.0.11.0</AssemblyVersion>
    <FileVersion>1.0.11.0</FileVersion>
    <Version>1.0.11</Version>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\Release</OutputPath>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Thumb.png" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Thumb.png" />
  </ItemGroup>

  
  <ItemGroup>
    <PackageReference Include="MediaBrowser.Common" Version="4.8.10" />
    <PackageReference Include="MediaBrowser.Server.Core" Version="4.8.10" />
  </ItemGroup>

  
  <ItemGroup>
    <Folder Include="Configuration\" />
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="Copy $(TargetDir)$(TargetFileName) $(SolutionDir)$(ConfigurationName)\$(TargetFileName) /y&#xD;&#xA;" />
  </Target>


</Project>
